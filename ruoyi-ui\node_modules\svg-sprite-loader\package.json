{"name": "svg-sprite-loader", "version": "5.1.1", "description": "Webpack loader for creating SVG sprites", "keywords": ["svg", "sprite", "svg sprite", "svg stack", "webpack", "webpack2", "webpack3", "webpack loader", "webpack plugin"], "homepage": "https://github.com/JetBrains/svg-sprite-loader#readme", "bugs": "https://github.com/JetBrains/svg-sprite-loader/issues", "license": "MIT", "author": "kisenka", "repository": "JetBrains/svg-sprite-loader", "main": "lib/loader.js", "files": ["examples/**", "lib/**", "runtime/*.js", "plugin.js", "README.md", "LICENSE"], "engines": {"node": ">=6"}, "dependencies": {"bluebird": "^3.5.0", "deepmerge": "1.3.2", "domready": "1.0.8", "escape-string-regexp": "1.0.5", "html-webpack-plugin": "^3.2.0", "loader-utils": "^1.1.0", "svg-baker": "^1.5.0", "svg-baker-runtime": "^1.4.7", "url-slug": "2.0.0"}, "devDependencies": {"babel-core": "^6.24.1", "babel-loader": "^6.4.1", "babel-plugin-transform-object-rest-spread": "6.23.0", "babel-preset-es2015": "^6.24.0", "babel-preset-react": "6.24.1", "chai": "^3.5.0", "ci-publish": "^1.3.1", "codeclimate-test-reporter": "0.4.1", "commitizen": "2.9.6", "css-loader": "^0.28.0", "cz-customizable": "^5.0.0", "eslint-config-airbnb-base": "^11.1.2", "eslint-plugin-import": "^2.2.0", "file-loader": "^3.0.1", "glob": "7.1.1", "html-loader": "^0.5.5", "husky": "^0.13.3", "istanbul": "^0.4.5", "memory-fs": "^0.4.1", "minimist": "^1.2.0", "mocha": "^7.0.0", "nyc": "^10.3.2", "pascal-case": "2.0.1", "react": "15.5.4", "react-dom": "15.5.4", "rollup": "^0.41.6", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-node-resolve": "^3.0.0", "shelljs": "0.7.7", "standard-version": "4.0.0", "svgo": "^0.7.2", "svgo-loader": "^1.2.1", "validate-commit-msg": "^2.12.1", "wallaby-webpack": "^3.9.15", "webpack-cli": "^3.3.10", "webpack-toolkit": "^1.0.0"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": ".cz-config.js"}}, "scripts": {"bootstrap": "node scripts/bootstrap", "build:examples": "yarn env webpack-3 && yarn run build:runtime && node scripts/build-examples-dll && node scripts/build-examples", "build:runtime": "node scripts/build-runtime.js", "commit": "git-cz", "commitmsg": "validate-commit-msg", "env": "node scripts/select-env", "lint": "eslint --quiet lib runtime scripts test", "precommit": "yarn lint", "pretest": "yarn build:runtime", "prerelease": "yarn build:runtime && yarn lint && yarn test:all", "release": "yarn build:examples && git add examples && standard-version -a && git push --follow-tags origin master", "release:dry-run": "standard-version --no-verify", "test": "mocha test/*.test.js", "test:all": "yarn test:webpack-2 && yarn test:webpack-3 && yarn test:webpack-4", "test:webpack-2": "yarn env webpack-2 && yarn test", "test:webpack-3": "yarn env webpack-3 && yarn test", "test:webpack-4": "yarn env webpack-4 && yarn test", "upload-coverage": "codeclimate-test-reporter < coverage/lcov.info"}}