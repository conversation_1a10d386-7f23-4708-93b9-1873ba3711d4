'use strict';

exports.__esModule = true;
exports.default = tokenize;
var SINGLE_QUOTE = 39;
var DOUBLE_QUOTE = 34;
var BACKSLASH = 92;
var SLASH = 47;
var NEWLINE = 10;
var SPACE = 32;
var FEED = 12;
var TAB = 9;
var CR = 13;
var OPEN_SQUARE = 91;
var CLOSE_SQUARE = 93;
var OPEN_PARENTHESES = 40;
var CLOSE_PARENTHESES = 41;
var OPEN_CURLY = 123;
var CLOSE_CURLY = 125;
var SEMICOLON = 59;
var ASTERISK = 42;
var COLON = 58;
var AT = 64;

var RE_AT_END = /[ \n\t\r\f\{\(\)'"\\;/\[\]#]/g;
var RE_WORD_END = /[ \n\t\r\f\(\)\{\}:;@!'"\\\]\[#]|\/(?=\*)/g;
var RE_BAD_BRACKET = /.[\\\/\("'\n]/;

function tokenize(input) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};

    var tokens = [];
    var css = input.css.valueOf();

    var ignore = options.ignoreErrors;

    var code = void 0,
        next = void 0,
        quote = void 0,
        lines = void 0,
        last = void 0,
        content = void 0,
        escape = void 0,
        nextLine = void 0,
        nextOffset = void 0,
        escaped = void 0,
        escapePos = void 0,
        prev = void 0,
        n = void 0;

    var length = css.length;
    var offset = -1;
    var line = 1;
    var pos = 0;

    function unclosed(what) {
        throw input.error('Unclosed ' + what, line, pos - offset);
    }

    while (pos < length) {
        code = css.charCodeAt(pos);

        if (code === NEWLINE || code === FEED || code === CR && css.charCodeAt(pos + 1) !== NEWLINE) {
            offset = pos;
            line += 1;
        }

        switch (code) {
            case NEWLINE:
            case SPACE:
            case TAB:
            case CR:
            case FEED:
                next = pos;
                do {
                    next += 1;
                    code = css.charCodeAt(next);
                    if (code === NEWLINE) {
                        offset = next;
                        line += 1;
                    }
                } while (code === SPACE || code === NEWLINE || code === TAB || code === CR || code === FEED);

                tokens.push(['space', css.slice(pos, next)]);
                pos = next - 1;
                break;

            case OPEN_SQUARE:
                tokens.push(['[', '[', line, pos - offset]);
                break;

            case CLOSE_SQUARE:
                tokens.push([']', ']', line, pos - offset]);
                break;

            case OPEN_CURLY:
                tokens.push(['{', '{', line, pos - offset]);
                break;

            case CLOSE_CURLY:
                tokens.push(['}', '}', line, pos - offset]);
                break;

            case COLON:
                tokens.push([':', ':', line, pos - offset]);
                break;

            case SEMICOLON:
                tokens.push([';', ';', line, pos - offset]);
                break;

            case OPEN_PARENTHESES:
                prev = tokens.length ? tokens[tokens.length - 1][1] : '';
                n = css.charCodeAt(pos + 1);
                if (prev === 'url' && n !== SINGLE_QUOTE && n !== DOUBLE_QUOTE && n !== SPACE && n !== NEWLINE && n !== TAB && n !== FEED && n !== CR) {
                    next = pos;
                    do {
                        escaped = false;
                        next = css.indexOf(')', next + 1);
                        if (next === -1) {
                            if (ignore) {
                                next = pos;
                                break;
                            } else {
                                unclosed('bracket');
                            }
                        }
                        escapePos = next;
                        while (css.charCodeAt(escapePos - 1) === BACKSLASH) {
                            escapePos -= 1;
                            escaped = !escaped;
                        }
                    } while (escaped);

                    tokens.push(['brackets', css.slice(pos, next + 1), line, pos - offset, line, next - offset]);
                    pos = next;
                } else {
                    next = css.indexOf(')', pos + 1);
                    content = css.slice(pos, next + 1);

                    if (next === -1 || RE_BAD_BRACKET.test(content)) {
                        tokens.push(['(', '(', line, pos - offset]);
                    } else {
                        tokens.push(['brackets', content, line, pos - offset, line, next - offset]);
                        pos = next;
                    }
                }

                break;

            case CLOSE_PARENTHESES:
                tokens.push([')', ')', line, pos - offset]);
                break;

            case SINGLE_QUOTE:
            case DOUBLE_QUOTE:
                quote = code === SINGLE_QUOTE ? '\'' : '"';
                next = pos;
                do {
                    escaped = false;
                    next = css.indexOf(quote, next + 1);
                    if (next === -1) {
                        if (ignore) {
                            next = pos + 1;
                            break;
                        } else {
                            unclosed('string');
                        }
                    }
                    escapePos = next;
                    while (css.charCodeAt(escapePos - 1) === BACKSLASH) {
                        escapePos -= 1;
                        escaped = !escaped;
                    }
                } while (escaped);

                content = css.slice(pos, next + 1);
                lines = content.split('\n');
                last = lines.length - 1;

                if (last > 0) {
                    nextLine = line + last;
                    nextOffset = next - lines[last].length;
                } else {
                    nextLine = line;
                    nextOffset = offset;
                }

                tokens.push(['string', css.slice(pos, next + 1), line, pos - offset, nextLine, next - nextOffset]);

                offset = nextOffset;
                line = nextLine;
                pos = next;
                break;

            case AT:
                RE_AT_END.lastIndex = pos + 1;
                RE_AT_END.test(css);
                if (RE_AT_END.lastIndex === 0) {
                    next = css.length - 1;
                } else {
                    next = RE_AT_END.lastIndex - 2;
                }
                tokens.push(['at-word', css.slice(pos, next + 1), line, pos - offset, line, next - offset]);
                pos = next;
                break;

            case BACKSLASH:
                next = pos;
                escape = true;
                while (css.charCodeAt(next + 1) === BACKSLASH) {
                    next += 1;
                    escape = !escape;
                }
                code = css.charCodeAt(next + 1);
                if (escape && code !== SLASH && code !== SPACE && code !== NEWLINE && code !== TAB && code !== CR && code !== FEED) {
                    next += 1;
                }
                tokens.push(['word', css.slice(pos, next + 1), line, pos - offset, line, next - offset]);
                pos = next;
                break;

            default:
                if (code === SLASH && css.charCodeAt(pos + 1) === ASTERISK) {
                    next = css.indexOf('*/', pos + 2) + 1;
                    if (next === 0) {
                        if (ignore) {
                            next = css.length;
                        } else {
                            unclosed('comment');
                        }
                    }

                    content = css.slice(pos, next + 1);
                    lines = content.split('\n');
                    last = lines.length - 1;

                    if (last > 0) {
                        nextLine = line + last;
                        nextOffset = next - lines[last].length;
                    } else {
                        nextLine = line;
                        nextOffset = offset;
                    }

                    tokens.push(['comment', content, line, pos - offset, nextLine, next - nextOffset]);

                    offset = nextOffset;
                    line = nextLine;
                    pos = next;
                } else {
                    RE_WORD_END.lastIndex = pos + 1;
                    RE_WORD_END.test(css);
                    if (RE_WORD_END.lastIndex === 0) {
                        next = css.length - 1;
                    } else {
                        next = RE_WORD_END.lastIndex - 2;
                    }

                    tokens.push(['word', css.slice(pos, next + 1), line, pos - offset, line, next - offset]);
                    pos = next;
                }

                break;
        }

        pos++;
    }

    return tokens;
}
module.exports = exports['default'];
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
